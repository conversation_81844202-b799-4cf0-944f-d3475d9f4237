version: '3.8'

services:
  cninfo-web:
    build: .
    container_name: cninfo-analysis
    ports:
      - "5000:5000"
    volumes:
      # 数据持久化：将容器内的数据目录映射到宿主机
      - ./data/txt:/app/txt
      - ./data/pdf:/app/pdf
      - ./data/database:/app/database
    environment:
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：Nginx反向代理（如果需要）
  # nginx:
  #   image: nginx:alpine
  #   container_name: cninfo-nginx
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   depends_on:
  #     - cninfo-web
  #   restart: unless-stopped
