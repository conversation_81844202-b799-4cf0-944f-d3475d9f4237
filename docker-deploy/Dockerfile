FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir numpy==1.24.3 && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app.py web_spider.py database.py run_web.py ./
COPY templates/ ./templates/
COPY static/ ./static/

# 创建数据目录
RUN mkdir -p txt pdf database

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000 || exit 1

# 设置环境变量确保Python输出不缓冲
ENV PYTHONUNBUFFERED=1

# 启动命令
CMD ["python", "-u", "run_web.py"]
