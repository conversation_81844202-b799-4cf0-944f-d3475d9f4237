"""
网页版爬虫模块 - 整合原有爬虫功能
"""
import requests
import random
import time
import os
import re
import pdfplumber
from typing import List, Dict, Optional, Tuple
from database import DatabaseManager


class WebSpider:
    def __init__(self, db_manager: DatabaseManager):
        """初始化网页爬虫"""
        self.db = db_manager
        self.is_running = False
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        # 请求头
        self.headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Host': 'www.cninfo.com.cn',
            'Origin': 'http://www.cninfo.com.cn',
            'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # API URLs
        self.orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
        self.query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
        self.download_base_url = 'http://static.cninfo.com.cn/'
    
    def get_orgid_by_code(self, stock_code: str) -> Optional[Dict]:
        """根据股票代码获取orgId"""
        try:
            response = requests.get(self.orgid_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        return {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
            return None
        except Exception as e:
            print(f"获取orgId失败: {e}")
            return None
    
    def search_announcements(self, stock_code: str, org_id: str, 
                           search_keyword: str = "年度报告", 
                           start_date: str = "2024-01-01", 
                           end_date: str = "2025-12-31") -> List[Dict]:
        """搜索公告"""
        try:
            self.headers['User-Agent'] = random.choice(self.user_agents)
            
            # 深市查询
            szse_query = {
                'pageNum': 1,
                'pageSize': 30,
                'tabName': 'fulltext',
                'column': 'szse',
                'stock': f'{stock_code},{org_id}',
                'searchkey': '',
                'secid': '',
                'plate': 'sz',
                'category': 'category_ndbg_szsh',
                'trade': '',
                'seDate': f'{start_date}~{end_date}',
                'sortName': '',
                'sortType': '',
                'isHLtitle': 'true'
            }
            
            # 沪市查询
            sse_query = szse_query.copy()
            sse_query.update({
                'column': 'sse',
                'plate': 'sh'
            })
            
            announcements = []
            
            # 查询深市
            try:
                print(f"  🔍 查询深市数据...")
                response = requests.post(self.query_url, headers=self.headers,
                                       data=szse_query, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"  📊 深市响应: {type(result)}")
                    szse_announcements = result.get('announcements', []) if result else []
                    if szse_announcements and isinstance(szse_announcements, list):
                        announcements.extend(szse_announcements)
                        print(f"  ✅ 深市找到 {len(szse_announcements)} 条公告")
                    else:
                        print(f"  ⚠️ 深市没有找到公告数据")
                else:
                    print(f"  ❌ 深市查询失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"深市查询失败: {e}")

            # 查询沪市
            try:
                print(f"  🔍 查询沪市数据...")
                response = requests.post(self.query_url, headers=self.headers,
                                       data=sse_query, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"  📊 沪市响应: {type(result)}")
                    sse_announcements = result.get('announcements', []) if result else []
                    if sse_announcements and isinstance(sse_announcements, list):
                        announcements.extend(sse_announcements)
                        print(f"  ✅ 沪市找到 {len(sse_announcements)} 条公告")
                    else:
                        print(f"  ⚠️ 沪市没有找到公告数据")
                else:
                    print(f"  ❌ 沪市查询失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"沪市查询失败: {e}")
            
            # 过滤公告
            filtered_announcements = []
            for announcement in announcements:
                title = announcement.get('announcementTitle', '')
                if (search_keyword in title and 
                    '摘要' not in title and 
                    '确认意见' not in title and
                    '招股书' not in title):
                    filtered_announcements.append(announcement)
            
            return filtered_announcements
            
        except Exception as e:
            print(f"搜索公告失败: {e}")
            return []
    
    def download_pdf(self, announcement: Dict, save_dir: str = "results/pdf") -> Optional[str]:
        """下载PDF文件"""
        try:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            adjunct_url = announcement.get('adjunctUrl', '')
            if not adjunct_url:
                return None
            
            download_url = self.download_base_url + adjunct_url
            
            # 构造文件名
            stock_code = announcement.get('secCode', '')
            company_name = announcement.get('secName', '')
            title = announcement.get('announcementTitle', '')
            
            # 清理文件名中的特殊字符
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
            file_name = f"{stock_code}_{company_name}_{safe_title}.pdf"
            file_path = os.path.join(save_dir, file_name)
            
            # 检查文件是否已存在
            if os.path.exists(file_path):
                return file_path
            
            # 下载文件
            self.headers['User-Agent'] = random.choice(self.user_agents)
            response = requests.get(download_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                return file_path
            else:
                print(f"下载失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"下载PDF失败: {e}")
            return None
    
    def convert_pdf_to_txt(self, pdf_path: str, txt_dir: str = "results/txt") -> Optional[str]:
        """将PDF转换为TXT"""
        try:
            if not os.path.exists(txt_dir):
                os.makedirs(txt_dir)
            
            # 构造TXT文件路径
            pdf_name = os.path.basename(pdf_path)
            txt_name = pdf_name.replace('.pdf', '.txt')
            txt_path = os.path.join(txt_dir, txt_name)
            
            # 检查TXT文件是否已存在
            if os.path.exists(txt_path):
                return txt_path
            
            # 提取文本
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            if text:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                return txt_path
            else:
                print(f"无法从PDF提取文本: {pdf_path}")
                return None
                
        except Exception as e:
            print(f"PDF转换失败: {e}")
            return None
    
    def analyze_keywords(self, txt_content: str, keywords: List[str]) -> Dict[str, int]:
        """分析关键词"""
        # 清理文本，只保留中文字符
        clean_content = re.sub(r'[^\u4e00-\u9fa5]', '', txt_content)
        
        # 统计关键词
        keyword_stats = {}
        for keyword in keywords:
            count = clean_content.count(keyword)
            keyword_stats[keyword] = count
        
        return keyword_stats
    
    def crawl_and_analyze(self, stock_codes: List[str], keywords: List[str],
                         search_keyword: str = "年度报告",
                         start_date: str = "2024-01-01",
                         end_date: str = "2025-12-31",
                         use_online: bool = True,
                         task_id: str = None,
                         related_parties: List[str] = None,
                         innovation_keywords: List[str] = None,
                         progress_callback=None) -> Dict:
        """爬取和分析主函数"""
        self.is_running = True
        results = {
            'success': True,
            'message': '',
            'downloaded_files': [],
            'analysis_results': {},
            'errors': []
        }

        # 使用统一的analysis_id
        analysis_id = task_id if task_id else f"analysis_{int(time.time())}"
        print(f"🆔 使用分析ID: {analysis_id}")
        
        total_steps = len(stock_codes)
        current_step = 0
        
        try:
            for stock_code in stock_codes:
                if not self.is_running:
                    break
                
                current_step += 1
                if progress_callback:
                    progress_callback(current_step, total_steps, f"处理股票: {stock_code}")
                
                # 获取公司信息
                if use_online:
                    stock_info = self.get_orgid_by_code(stock_code)
                    if not stock_info:
                        error_msg = f"无法获取股票 {stock_code} 的信息"
                        results['errors'].append(error_msg)
                        continue
                    
                    company_name = stock_info['zwjc']
                    org_id = stock_info['orgId']
                    
                    # 添加公司信息到数据库
                    self.db.add_company(stock_code, company_name, org_id)
                    
                    # 搜索公告
                    announcements = self.search_announcements(
                        stock_code, org_id, search_keyword, start_date, end_date
                    )
                    
                    if not announcements:
                        error_msg = f"股票 {stock_code} 没有找到相关公告"
                        results['errors'].append(error_msg)
                        continue
                    
                    # 下载和转换
                    for announcement in announcements:
                        if not self.is_running:
                            break
                        
                        # 下载PDF
                        pdf_path = self.download_pdf(announcement)
                        if not pdf_path:
                            continue
                        
                        results['downloaded_files'].append(pdf_path)
                        
                        # 转换为TXT
                        txt_path = self.convert_pdf_to_txt(pdf_path)
                        if not txt_path:
                            continue
                        
                        # 读取TXT内容
                        with open(txt_path, 'r', encoding='utf-8') as f:
                            txt_content = f.read()
                        
                        # 添加年报到数据库
                        report_title = announcement.get('announcementTitle', '')
                        file_name = os.path.basename(txt_path)
                        report_id = self.db.add_report(
                            stock_code=stock_code,
                            company_name=company_name,
                            report_title=report_title,
                            file_name=file_name,
                            file_path=txt_path,
                            txt_content=txt_content
                        )
                        
                        # 关键词分析
                        if keywords and report_id:
                            keyword_stats = self.analyze_keywords(txt_content, keywords)

                            # 保存分析结果（使用统一的analysis_id）
                            self.db.save_keyword_analysis(analysis_id, stock_code, report_id, keyword_stats)

                            if stock_code not in results['analysis_results']:
                                results['analysis_results'][stock_code] = {}
                            results['analysis_results'][stock_code][file_name] = keyword_stats
                
                else:
                    # 使用本地数据库
                    reports = self.db.get_reports_by_stock_codes([stock_code])
                    if not reports:
                        error_msg = f"本地数据库中没有找到股票 {stock_code} 的年报"
                        results['errors'].append(error_msg)
                        continue
                    
                    # 分析本地年报
                    for report in reports:
                        if not self.is_running:
                            break
                        
                        if keywords and report['txt_content']:
                            keyword_stats = self.analyze_keywords(report['txt_content'], keywords)

                            # 保存分析结果（使用统一的analysis_id）
                            self.db.save_keyword_analysis(analysis_id, stock_code, report['id'], keyword_stats)

                            if stock_code not in results['analysis_results']:
                                results['analysis_results'][stock_code] = {}
                            results['analysis_results'][stock_code][report['file_name']] = keyword_stats
                
                # 添加随机延时
                if use_online:
                    time.sleep(random.uniform(1, 3))
            
            results['message'] = f"处理完成，共处理 {current_step} 个股票"
            
        except Exception as e:
            results['success'] = False
            results['message'] = f"处理过程中出现错误: {str(e)}"
            results['errors'].append(str(e))
        
        finally:
            self.is_running = False
        
        return results
    
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
