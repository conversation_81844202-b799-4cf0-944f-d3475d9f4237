#!/usr/bin/env python3
"""
网页版启动脚本
"""
import os
import sys
from app import app, db_manager

def main():
    """主函数"""
    print("=" * 60)
    print("🌐 巨潮资讯网年报爬虫工具 - 网页版")
    print("=" * 60)
    
    # 检查并创建必要的目录
    directories = [
        'results/pdf',
        'results/txt', 
        'exports',
        'templates',
        'static/css',
        'static/js'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
    
    # 初始化数据库
    print("\n📊 初始化数据库...")
    try:
        db_manager.init_database()
        print("✅ 数据库初始化完成")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 检查是否需要导入txt文件
    txt_dir = "txt"
    if os.path.exists(txt_dir):
        txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
        if txt_files:
            print(f"\n📁 发现 {len(txt_files)} 个TXT文件")
            choice = input("是否导入到数据库？(y/n): ").lower().strip()
            if choice == 'y':
                print("📥 正在导入TXT文件...")
                result = db_manager.import_txt_files(txt_dir)
                print(f"✅ 导入完成：成功 {result['imported']} 个，跳过 {result['skipped']} 个，失败 {result['errors']} 个")
    
    print("\n🚀 启动Web服务器...")
    print("📍 访问地址: http://localhost:5000")
    print("📍 局域网访问: http://0.0.0.0:5000")
    print("\n💡 使用说明:")
    print("   1. 选择数据源：在线下载PDF 或 使用本地数据库")
    print("   2. 输入股票代码（每行一个）")
    print("   3. 输入关键词（每行一个）")
    print("   4. 点击'开始分析'或'仅关键词分析'")
    print("   5. 查看结果并导出Excel")
    print("\n按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")

if __name__ == '__main__':
    main()
