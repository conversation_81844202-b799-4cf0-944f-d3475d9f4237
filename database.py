"""
数据库操作模块 - SQLite数据库管理
"""
import sqlite3
import os
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple


class DatabaseManager:
    def __init__(self, db_path: str = "cninfo_reports.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 公司信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS companies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    company_name TEXT NOT NULL,
                    org_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 年报信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    company_name TEXT NOT NULL,
                    report_title TEXT NOT NULL,
                    report_year INTEGER,
                    file_name TEXT NOT NULL,
                    file_path TEXT,
                    txt_content TEXT,
                    file_size INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (stock_code) REFERENCES companies (stock_code)
                )
            ''')
            
            # 关键词分析结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_id TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    report_id INTEGER,
                    keyword TEXT NOT NULL,
                    count INTEGER DEFAULT 0,
                    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (report_id) REFERENCES reports (id),
                    FOREIGN KEY (stock_code) REFERENCES companies (stock_code)
                )
            ''')
            
            # 分析任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE NOT NULL,
                    task_name TEXT NOT NULL,
                    keywords TEXT NOT NULL,
                    stock_codes TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    progress INTEGER DEFAULT 0,
                    result_file TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_reports_stock_code ON reports(stock_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_analysis_stock_code ON keyword_analysis(stock_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_analysis_analysis_id ON keyword_analysis(analysis_id)')
            
            conn.commit()
    
    def add_company(self, stock_code: str, company_name: str, org_id: str = None) -> bool:
        """添加公司信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO companies (stock_code, company_name, org_id, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (stock_code, company_name, org_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加公司信息失败: {e}")
            return False
    
    def add_report(self, stock_code: str, company_name: str, report_title: str,
                   file_name: str, file_path: str = None, txt_content: str = None) -> Optional[int]:
        """添加年报信息"""
        try:
            # 提取年份
            year_match = re.search(r'(\d{4})年', report_title)
            report_year = int(year_match.group(1)) if year_match else None

            # 获取文件大小
            file_size = None
            if file_path and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 先检查是否已存在相同的记录
                cursor.execute('''
                    SELECT id FROM reports
                    WHERE stock_code = ? AND file_name = ?
                ''', (stock_code, file_name))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    cursor.execute('''
                        UPDATE reports
                        SET company_name = ?, report_title = ?, report_year = ?,
                            file_path = ?, txt_content = ?, file_size = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (company_name, report_title, report_year,
                          file_path, txt_content, file_size, existing[0]))
                    conn.commit()
                    return existing[0]
                else:
                    # 插入新记录
                    cursor.execute('''
                        INSERT INTO reports
                        (stock_code, company_name, report_title, report_year, file_name,
                         file_path, txt_content, file_size, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (stock_code, company_name, report_title, report_year,
                          file_name, file_path, txt_content, file_size))
                    conn.commit()
                    return cursor.lastrowid
        except Exception as e:
            print(f"添加年报信息失败: {e}")
            return None
    
    def get_companies(self) -> List[Dict]:
        """获取所有公司信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM companies ORDER BY stock_code')
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取公司信息失败: {e}")
            return []
    
    def get_reports_by_stock_codes(self, stock_codes: List[str]) -> List[Dict]:
        """根据股票代码获取年报信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                placeholders = ','.join(['?' for _ in stock_codes])
                cursor.execute(f'''
                    SELECT * FROM reports 
                    WHERE stock_code IN ({placeholders})
                    ORDER BY stock_code, report_year DESC
                ''', stock_codes)
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取年报信息失败: {e}")
            return []
    
    def save_keyword_analysis(self, analysis_id: str, stock_code: str, 
                            report_id: int, keyword_stats: Dict[str, int]) -> bool:
        """保存关键词分析结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除旧的分析结果
                cursor.execute('''
                    DELETE FROM keyword_analysis 
                    WHERE analysis_id = ? AND stock_code = ? AND report_id = ?
                ''', (analysis_id, stock_code, report_id))
                
                # 插入新的分析结果
                for keyword, count in keyword_stats.items():
                    cursor.execute('''
                        INSERT INTO keyword_analysis 
                        (analysis_id, stock_code, report_id, keyword, count)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (analysis_id, stock_code, report_id, keyword, count))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"保存关键词分析结果失败: {e}")
            return False
    
    def get_keyword_analysis(self, analysis_id: str) -> List[Dict]:
        """获取关键词分析结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT ka.*, r.company_name, r.report_title, r.file_name
                    FROM keyword_analysis ka
                    LEFT JOIN reports r ON ka.report_id = r.id
                    WHERE ka.analysis_id = ?
                    ORDER BY ka.stock_code, ka.keyword
                ''', (analysis_id,))
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取关键词分析结果失败: {e}")
            return []

    def clean_duplicate_reports(self):
        """清理重复的年报记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 找到重复的记录（基于stock_code和file_name）
                cursor.execute('''
                    DELETE FROM reports
                    WHERE id NOT IN (
                        SELECT MIN(id)
                        FROM reports
                        GROUP BY stock_code, file_name
                    )
                ''')

                deleted_count = cursor.rowcount
                conn.commit()
                print(f"清理了 {deleted_count} 条重复的年报记录")
                return deleted_count
        except Exception as e:
            print(f"清理重复记录失败: {e}")
            return 0

    def clean_duplicate_keyword_analysis(self):
        """清理重复的关键词分析记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 找到重复的分析记录（基于analysis_id, stock_code, report_id, keyword）
                cursor.execute('''
                    DELETE FROM keyword_analysis
                    WHERE id NOT IN (
                        SELECT MIN(id)
                        FROM keyword_analysis
                        GROUP BY analysis_id, stock_code, report_id, keyword
                    )
                ''')

                deleted_count = cursor.rowcount
                conn.commit()
                print(f"清理了 {deleted_count} 条重复的关键词分析记录")
                return deleted_count
        except Exception as e:
            print(f"清理重复分析记录失败: {e}")
            return 0

    def clean_duplicate_reports(self):
        """清理重复的年报记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 找到重复的记录（基于stock_code和file_name）
                cursor.execute('''
                    DELETE FROM reports
                    WHERE id NOT IN (
                        SELECT MIN(id)
                        FROM reports
                        GROUP BY stock_code, file_name
                    )
                ''')

                deleted_count = cursor.rowcount
                conn.commit()
                print(f"清理了 {deleted_count} 条重复的年报记录")
                return deleted_count
        except Exception as e:
            print(f"清理重复记录失败: {e}")
            return 0

    def clean_duplicate_keyword_analysis(self):
        """清理重复的关键词分析记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 找到重复的分析记录
                cursor.execute('''
                    DELETE FROM keyword_analysis
                    WHERE id NOT IN (
                        SELECT MIN(id)
                        FROM keyword_analysis
                        GROUP BY analysis_id, stock_code, report_id, keyword
                    )
                ''')

                deleted_count = cursor.rowcount
                conn.commit()
                print(f"清理了 {deleted_count} 条重复的关键词分析记录")
                return deleted_count
        except Exception as e:
            print(f"清理重复分析记录失败: {e}")
            return 0
    
    def create_analysis_task(self, task_id: str, task_name: str, 
                           keywords: List[str], stock_codes: List[str]) -> bool:
        """创建分析任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO analysis_tasks 
                    (task_id, task_name, keywords, stock_codes, status, progress)
                    VALUES (?, ?, ?, ?, 'pending', 0)
                ''', (task_id, task_name, '\n'.join(keywords), '\n'.join(stock_codes)))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建分析任务失败: {e}")
            return False
    
    def update_task_progress(self, task_id: str, progress: int, status: str = None) -> bool:
        """更新任务进度"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if status:
                    cursor.execute('''
                        UPDATE analysis_tasks 
                        SET progress = ?, status = ?, 
                            completed_at = CASE WHEN ? = 'completed' THEN CURRENT_TIMESTAMP ELSE completed_at END
                        WHERE task_id = ?
                    ''', (progress, status, status, task_id))
                else:
                    cursor.execute('''
                        UPDATE analysis_tasks SET progress = ? WHERE task_id = ?
                    ''', (progress, task_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新任务进度失败: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM analysis_tasks WHERE task_id = ?', (task_id,))
                row = cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None
        except Exception as e:
            print(f"获取任务状态失败: {e}")
            return None
    
    def import_txt_files(self, txt_dir: str = "txt") -> int:
        """导入txt文件夹中的年报文本到数据库"""
        imported_count = 0
        
        # 检查txt文件夹
        if not os.path.exists(txt_dir):
            print(f"txt文件夹不存在: {txt_dir}")
            return 0
        
        # 遍历txt文件
        for root, dirs, files in os.walk(txt_dir):
            for file in files:
                if file.endswith('.txt'):
                    file_path = os.path.join(root, file)
                    
                    # 解析文件名获取信息
                    file_name_without_ext = os.path.splitext(file)[0]
                    parts = file_name_without_ext.split('_')
                    
                    if len(parts) >= 3:
                        stock_code = parts[0]
                        company_name = parts[1]
                        report_title = '_'.join(parts[2:])
                        
                        # 读取文件内容
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                txt_content = f.read()
                            
                            # 添加公司信息
                            self.add_company(stock_code, company_name)
                            
                            # 添加年报信息
                            report_id = self.add_report(
                                stock_code=stock_code,
                                company_name=company_name,
                                report_title=report_title,
                                file_name=file,
                                file_path=file_path,
                                txt_content=txt_content
                            )
                            
                            if report_id:
                                imported_count += 1
                                print(f"导入成功: {file}")
                            
                        except Exception as e:
                            print(f"导入失败 {file}: {e}")
        
        print(f"总共导入了 {imported_count} 个年报文件")
        return imported_count
    
    def search_reports(self, keyword: str = None, stock_codes: List[str] = None, 
                      year: int = None) -> List[Dict]:
        """搜索年报"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM reports WHERE 1=1"
                params = []
                
                if keyword:
                    query += " AND (report_title LIKE ? OR txt_content LIKE ?)"
                    params.extend([f"%{keyword}%", f"%{keyword}%"])
                
                if stock_codes:
                    placeholders = ','.join(['?' for _ in stock_codes])
                    query += f" AND stock_code IN ({placeholders})"
                    params.extend(stock_codes)
                
                if year:
                    query += " AND report_year = ?"
                    params.append(year)
                
                query += " ORDER BY stock_code, report_year DESC"
                
                cursor.execute(query, params)
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"搜索年报失败: {e}")
            return []
