# 巨潮资讯网年报爬虫工具 - 网页版

## 项目简介

这是巨潮资讯网年报爬虫工具的网页版本，将原有的GUI应用转换为基于Flask的Web应用，支持在线PDF下载和本地数据库两种数据源模式。

## 主要功能

### 🔄 数据源模式
- **在线下载模式**：从巨潮资讯网实时下载PDF年报
- **本地数据库模式**：使用SQLite数据库存储的年报文本数据

### 📊 核心功能
- 股票代码批量输入
- 年报PDF自动下载和文本转换
- 关键词统计分析
- 结果可视化展示
- Excel报告导出
- 任务进度实时监控

### 💾 数据管理
- SQLite数据库存储年报数据
- 自动导入txt文件夹中的年报文本
- 公司信息和分析结果持久化存储

## 安装和运行

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements_web.txt
```

### 2. 启动应用

```bash
# 方式1：使用启动脚本（推荐）
python run_web.py

# 方式2：直接运行Flask应用
python app.py
```

### 3. 访问应用

- 本地访问：http://localhost:5000
- 局域网访问：http://你的IP地址:5000

## 使用说明

### 数据源选择

#### 在线下载模式
1. 选择"在线下载PDF"
2. 输入股票代码（每行一个）
3. 设置搜索关键字（默认：年度报告）
4. 选择时间范围（默认：2024-01-01 到 2025-12-31）
5. 输入统计关键词
6. 点击"开始分析"

#### 本地数据库模式
1. 选择"使用本地数据库"
2. 输入股票代码（每行一个）
3. 输入统计关键词
4. 点击"仅关键词分析"

### 数据导入

如果你有txt文件夹中的年报文本文件：
1. 点击"导入TXT文件"按钮
2. 系统会自动扫描txt文件夹并导入到数据库
3. 导入后可以使用本地数据库模式进行分析

### 结果查看和导出

1. 分析完成后，结果会显示在右侧面板
2. 点击"导出Excel"可以下载分析结果
3. 点击"统计摘要"查看详细统计信息

## 技术架构

### 后端技术栈
- **Flask**：Web框架
- **SQLite**：数据库
- **pandas**：数据处理
- **pdfplumber**：PDF文本提取
- **requests**：HTTP请求

### 前端技术栈
- **Bootstrap 5**：UI框架
- **JavaScript**：交互逻辑
- **Bootstrap Icons**：图标库

### 数据库设计

#### companies表
- 存储公司基本信息（股票代码、公司名称、orgId等）

#### reports表
- 存储年报信息（标题、年份、文件路径、文本内容等）

#### keyword_analysis表
- 存储关键词分析结果

#### analysis_tasks表
- 存储分析任务状态和进度

## API接口

### 主要接口

- `GET /` - 主页面
- `POST /api/start_analysis` - 开始分析任务
- `GET /api/task_status/<task_id>` - 获取任务状态
- `POST /api/stop_task/<task_id>` - 停止任务
- `POST /api/keyword_analysis` - 仅关键词分析
- `POST /api/import_txt` - 导入TXT文件
- `GET /api/export_results/<task_id>` - 导出结果

## 文件结构

```
cninfo_process/
├── app.py                 # Flask主应用
├── database.py           # 数据库操作模块
├── web_spider.py         # 网页版爬虫模块
├── run_web.py           # 启动脚本
├── requirements_web.txt  # 依赖包列表
├── templates/
│   └── index.html       # 主页模板
├── static/
│   ├── css/
│   │   └── style.css    # 样式文件
│   └── js/
│       └── app.js       # 前端逻辑
├── results/             # 结果文件夹
│   ├── pdf/            # PDF文件
│   ├── txt/            # 文本文件
│   └── excel/          # Excel文件
├── exports/             # 导出文件
├── txt/                # 原始txt文件
└── cninfo_reports.db   # SQLite数据库
```

## 与GUI版本的对比

### 优势
- **跨平台访问**：任何设备的浏览器都可以访问
- **多用户支持**：支持多人同时使用
- **数据持久化**：SQLite数据库存储，数据不丢失
- **实时监控**：任务进度实时更新
- **响应式设计**：适配不同屏幕尺寸

### 功能对等
- ✅ 股票代码批量输入
- ✅ 在线PDF下载
- ✅ 关键词统计分析
- ✅ 结果展示和导出
- ✅ 进度监控
- ✅ 日志输出
- ✅ 本地数据支持

## 注意事项

1. **网络要求**：在线下载模式需要稳定的网络连接
2. **存储空间**：PDF和数据库文件会占用一定存储空间
3. **并发限制**：建议同时运行的分析任务不超过3个
4. **浏览器兼容性**：推荐使用Chrome、Firefox、Edge等现代浏览器

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :5000
   # 修改app.py中的端口号
   ```

2. **数据库锁定**
   ```bash
   # 删除数据库文件重新初始化
   rm cninfo_reports.db
   ```

3. **依赖包安装失败**
   ```bash
   # 使用国内镜像源
   pip install -r requirements_web.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

## 开发和扩展

### 添加新功能
1. 在`app.py`中添加新的API接口
2. 在`templates/index.html`中添加UI元素
3. 在`static/js/app.js`中添加前端逻辑

### 数据库扩展
1. 在`database.py`中修改表结构
2. 添加新的数据操作方法

## 许可证

本项目遵循原项目的许可证条款。

## 更新日志

### v1.0.0 (2024-07-04)
- 初始版本发布
- 完整的Web界面实现
- SQLite数据库支持
- 在线下载和本地数据双模式
- 实时进度监控
- Excel导出功能
