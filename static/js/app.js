// 全局变量
let currentTaskId = null;
let progressInterval = null;
let currentAnalysisId = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化界面状态
    updateDataSourceVisibility();
    
    // 添加初始日志
    addLog('系统已就绪，请选择操作...', 'info');
}

function bindEventListeners() {
    // 数据源选择变化
    document.querySelectorAll('input[name="dataSource"]').forEach(radio => {
        radio.addEventListener('change', updateDataSourceVisibility);
    });
    
    // 按钮事件
    document.getElementById('startAnalysisBtn').addEventListener('click', startAnalysis);
    document.getElementById('stopAnalysisBtn').addEventListener('click', stopAnalysis);
    document.getElementById('keywordOnlyBtn').addEventListener('click', keywordOnlyAnalysis);
    document.getElementById('importTxtBtn').addEventListener('click', importTxtFiles);
    document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    document.getElementById('exportResultsBtn').addEventListener('click', exportResults);
    document.getElementById('showSummaryBtn').addEventListener('click', showSummary);
    document.getElementById('cleanDuplicatesBtn').addEventListener('click', cleanDuplicates);


}

function updateDataSourceVisibility() {
    const isOnline = document.getElementById('onlineSource').checked;
    const searchKeywordGroup = document.getElementById('searchKeywordGroup');
    const dateRangeGroup = document.getElementById('dateRangeGroup');
    
    if (isOnline) {
        searchKeywordGroup.style.display = 'block';
        dateRangeGroup.style.display = 'block';
    } else {
        searchKeywordGroup.style.display = 'none';
        dateRangeGroup.style.display = 'none';
    }
}

// 全局强制关闭所有弹窗的函数
function forceCloseAllModals() {
    try {
        // 关闭所有Bootstrap模态框
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');
            modal.removeAttribute('aria-modal');
            modal.removeAttribute('role');
        });

        // 清理body状态
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // 移除所有backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        console.log('强制关闭所有弹窗完成');
    } catch (error) {
        console.error('强制关闭弹窗失败:', error);
    }
}

// 添加键盘快捷键 Esc 强制关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        forceCloseAllModals();
    }
});

function addLog(message, type = 'info') {
    const logOutput = document.getElementById('logOutput');
    const timestamp = new Date().toLocaleString('zh-CN');
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `
        <span class="timestamp">[${timestamp}]</span>
        <span class="message">${message}</span>
    `;
    
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

function clearLog() {
    const logOutput = document.getElementById('logOutput');
    logOutput.innerHTML = '';
    addLog('日志已清空', 'info');
}

function showLoading(text = '处理中...') {
    document.getElementById('loadingText').textContent = text;
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    try {
        const loadingModal = document.getElementById('loadingModal');

        // 方法1: 使用Bootstrap Modal API
        const modal = bootstrap.Modal.getInstance(loadingModal);
        if (modal) {
            modal.hide();
        }

        // 方法2: 立即强制关闭
        if (loadingModal) {
            loadingModal.style.display = 'none';
            loadingModal.classList.remove('show');
            loadingModal.setAttribute('aria-hidden', 'true');
            loadingModal.removeAttribute('aria-modal');
            loadingModal.removeAttribute('role');
        }

        // 立即清理body状态
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // 立即移除所有backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // 方法3: 延迟再次强制关闭（确保一定关闭）
        setTimeout(() => {
            if (loadingModal) {
                loadingModal.style.display = 'none';
                loadingModal.classList.remove('show');
                loadingModal.setAttribute('aria-hidden', 'true');
                loadingModal.removeAttribute('aria-modal');
                loadingModal.removeAttribute('role');
            }

            // 再次清理body状态
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // 再次移除所有backdrop
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
        }, 50);

        // 方法4: 最后一次强制清理
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 200);

    } catch (error) {
        console.error('关闭加载弹窗失败:', error);
        // 即使出错也要强制清理
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }
}

function updateProgress(progress, message, status = 'running') {
    const progressCard = document.getElementById('progressCard');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const taskStatus = document.getElementById('taskStatus');
    
    progressCard.style.display = 'block';
    progressBar.style.width = `${progress}%`;
    progressBar.textContent = `${progress}%`;
    progressText.textContent = message;
    
    // 更新状态徽章
    let badgeClass = 'bg-secondary';
    let statusText = '等待中';
    
    switch (status) {
        case 'running':
            badgeClass = 'bg-primary';
            statusText = '运行中';
            break;
        case 'completed':
            badgeClass = 'bg-success';
            statusText = '已完成';
            break;
        case 'error':
            badgeClass = 'bg-danger';
            statusText = '错误';
            break;
        case 'stopped':
            badgeClass = 'bg-warning';
            statusText = '已停止';
            break;
    }
    
    taskStatus.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;
}

function startAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();
    const searchKeyword = document.getElementById('searchKeyword').value.trim();
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const useOnline = document.getElementById('onlineSource').checked;
    const relatedParties = document.getElementById('relatedParties').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入关键词');
        return;
    }

    // 禁用开始按钮，启用停止按钮
    document.getElementById('startAnalysisBtn').disabled = true;
    document.getElementById('stopAnalysisBtn').disabled = false;

    let logMessage = '开始分析任务...';
    if (relatedParties) {
        logMessage += ' (包含关联方分析)';
    }

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        search_keyword: searchKeyword,
        start_date: startDate,
        end_date: endDate,
        use_online: useOnline,
        related_parties: relatedParties
    };



    addLog(logMessage, 'info');
    
    fetch('/api/start_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentTaskId = data.task_id;
            addLog(`任务已启动，任务ID: ${currentTaskId}`, 'success');
            startProgressMonitoring();
        } else {
            addLog(`启动失败: ${data.message}`, 'error');
            resetButtons();
        }
    })
    .catch(error => {
        addLog(`请求失败: ${error.message}`, 'error');
        resetButtons();
    });
}

function stopAnalysis() {
    if (!currentTaskId) {
        return;
    }
    
    fetch(`/api/stop_task/${currentTaskId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('任务已停止', 'warning');
        } else {
            addLog(`停止失败: ${data.message}`, 'error');
        }
        stopProgressMonitoring();
        resetButtons();
    })
    .catch(error => {
        addLog(`停止请求失败: ${error.message}`, 'error');
    });
}

function keywordOnlyAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();
    const relatedParties = document.getElementById('relatedParties').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入关键词');
        return;
    }

    showLoading('正在分析关键词...');
    let logMessage = '开始关键词分析...';
    if (relatedParties) {
        logMessage += ' (包含关联方分析)';
    }
    addLog(logMessage, 'info');

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        related_parties: relatedParties
    };

    fetch('/api/keyword_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 确保关闭加载弹窗
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            currentAnalysisId = data.analysis_id;
            currentTaskId = null; // 清除任务ID，使用分析ID
            addLog(`关键词分析完成: ${data.message}`, 'success');
            displayResults(data.data);

            // 显示关联方分析结果
            if (data.related_party_analysis && Object.keys(data.related_party_analysis).length > 0) {
                displayRelatedPartyAnalysis(data.related_party_analysis);
                addLog('🤝 关联方协同创新分析完成', 'success');
            }

            // 启用导出和摘要按钮
            document.getElementById('exportResultsBtn').disabled = false;
            document.getElementById('showSummaryBtn').disabled = false;

            addLog('💡 提示：现在可以点击数字旁边的眼睛图标查看关键词上下文', 'info');
        } else {
            addLog(`分析失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('关键词分析请求失败:', error);
        // 确保在错误情况下也关闭加载弹窗
        hideLoading();
        addLog(`分析请求失败: ${error.message}`, 'error');

        // 强制关闭弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);
    });
}

function importTxtFiles() {
    showLoading('正在导入TXT文件...');
    addLog('开始导入TXT文件到数据库...', 'info');

    fetch('/api/import_txt', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({txt_dir: 'txt'})
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            addLog(`导入完成: ${data.message}`, 'success');

            // 显示详细统计信息
            if (data.imported > 0) {
                addLog(`✅ 新增导入: ${data.imported} 个文件`, 'success');
            }
            if (data.skipped > 0) {
                addLog(`⏭️ 跳过重复: ${data.skipped} 个文件`, 'info');
            }
            if (data.errors > 0) {
                addLog(`❌ 导入失败: ${data.errors} 个文件`, 'warning');
            }

            addLog(`📊 总计处理: ${data.total} 个文件`, 'info');

            // 如果有新导入的文件，建议刷新
            if (data.imported > 0) {
                addLog('💡 提示：有新文件导入，建议刷新页面查看最新数据', 'info');
            }
        } else {
            addLog(`导入失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        addLog(`导入请求失败: ${error.message}`, 'error');
    });
}

function startProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    progressInterval = setInterval(() => {
        if (!currentTaskId) {
            stopProgressMonitoring();
            return;
        }
        
        fetch(`/api/task_status/${currentTaskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskData = data.data;
                updateProgress(taskData.progress, taskData.message, taskData.status);
                
                if (taskData.status === 'completed') {
                    addLog('任务完成！', 'success');
                    stopProgressMonitoring();
                    resetButtons();
                    loadTaskResults();
                } else if (taskData.status === 'error') {
                    addLog(`任务失败: ${taskData.message}`, 'error');
                    stopProgressMonitoring();
                    resetButtons();
                } else if (taskData.status === 'stopped') {
                    addLog('任务已停止', 'warning');
                    stopProgressMonitoring();
                    resetButtons();
                }
            }
        })
        .catch(error => {
            console.error('获取任务状态失败:', error);
        });
    }, 2000); // 每2秒检查一次
}

function stopProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

function resetButtons() {
    document.getElementById('startAnalysisBtn').disabled = false;
    document.getElementById('stopAnalysisBtn').disabled = true;
}

function loadTaskResults() {
    if (!currentTaskId) {
        return;
    }

    fetch(`/api/analysis_results/${currentTaskId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 设置分析ID，使上下文查看功能可用
            currentAnalysisId = currentTaskId; // 对于"开始分析"，使用task_id作为analysis_id
            displayResults(data.data);

            // 启用导出和摘要按钮
            document.getElementById('exportResultsBtn').disabled = false;
            document.getElementById('showSummaryBtn').disabled = false;

            addLog('💡 提示：现在可以点击数字旁边的眼睛图标查看关键词上下文', 'info');
        } else {
            addLog(`获取结果失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`获取结果请求失败: ${error.message}`, 'error');
    });
}

function displayResults(results) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');

    // 调试信息
    console.log('displayResults 收到的数据:', results);
    console.log('数据类型:', typeof results);
    console.log('是否为数组:', Array.isArray(results));

    if (!results) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    // 检查数据格式
    if (Array.isArray(results) && results.length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    if (typeof results === 'object' && !Array.isArray(results) && Object.keys(results).length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    let html = '';

    // 如果是数组格式（从数据库获取的结果）
    if (Array.isArray(results)) {
        console.log('使用数组格式处理，数据长度:', results.length);
        if (results.length > 0) {
            console.log('第一条数据示例:', results[0]);
        }
        html = generateTableFromArray(results);
    } else {
        // 如果是对象格式（直接分析的结果）
        console.log('使用对象格式处理，键数量:', Object.keys(results).length);
        if (Object.keys(results).length > 0) {
            console.log('第一个股票数据示例:', Object.entries(results)[0]);
        }
        html = generateTableFromObject(results);
    }

    // 添加分页功能
    const paginatedHtml = addPaginationToResults(html);
    resultsContent.innerHTML = paginatedHtml;
    resultsCard.style.display = 'block';

    // 启用导出和摘要按钮
    const exportBtn = document.getElementById('exportResultsBtn');
    const summaryBtn = document.getElementById('showSummaryBtn');

    if (exportBtn) exportBtn.disabled = false;
    if (summaryBtn) summaryBtn.disabled = false;

    // 添加成功日志
    addLog('结果显示完成，可以查看分析结果', 'success');
}

function generateTableFromObject(results) {
    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th style="min-width: 100px;">股票代码</th><th style="min-width: 200px;">文件名</th>';

    // 获取所有关键词
    const allKeywords = new Set();
    Object.values(results).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.keys(fileData).forEach(keyword => allKeywords.add(keyword));
        });
    });

    // 按关键词长度排序，确保显示一致性
    const sortedKeywords = Array.from(allKeywords).sort();

    sortedKeywords.forEach(keyword => {
        html += `<th class="keyword-cell text-center" style="min-width: 80px; font-weight: bold; background-color: #f8f9fa;">${keyword}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 生成数据行
    Object.entries(results).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            html += `<tr><td><strong>${stockCode}</strong></td><td title="${fileName}">${fileName}</td>`;
            sortedKeywords.forEach(keyword => {
                const count = fileData[keyword] || 0;
                const cellClass = count === 0 ? 'count-zero' : (count > 10 ? 'count-high' : '');
                let cellContent = count;

                // 如果有出现次数，添加查看上下文按钮
                if (count > 0 && (currentAnalysisId || currentTaskId)) {
                    cellContent = `
                        <div class="d-flex align-items-center justify-content-center">
                            <span class="me-2">${count}</span>
                            <button class="btn btn-sm btn-outline-info" onclick="showKeywordContext('${keyword}', '${stockCode}')" title="查看上下文">
                                <i class="bi bi-eye" style="font-size: 0.8em;"></i>
                            </button>
                        </div>
                    `;
                }

                html += `<td class="count-cell text-center ${cellClass}" title="${keyword}: ${count}次">${cellContent}</td>`;
            });
            html += '</tr>';
        });
    });
    
    html += '</tbody></table></div>';
    return html;
}

function generateTableFromArray(results) {
    if (results.length === 0) {
        return '<p class="text-muted">没有分析结果</p>';
    }

    // 前端去重：基于 stock_code + file_name + keyword 的组合
    const uniqueResults = [];
    const seen = new Set();

    results.forEach(result => {
        const key = `${result.stock_code}_${result.file_name}_${result.keyword}`;
        if (!seen.has(key)) {
            seen.add(key);
            uniqueResults.push(result);
        }
    });

    console.log(`原始数据: ${results.length} 条，去重后: ${uniqueResults.length} 条`);

    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th>股票代码</th><th>公司名称</th><th>文件名</th><th>关键词</th><th>出现次数</th><th>分析日期</th></tr></thead><tbody>';

    uniqueResults.forEach(result => {
        const count = result.count || 0;
        const cellClass = count === 0 ? 'count-zero' : (count > 10 ? 'count-high' : '');
        const analysisDate = result.analysis_date ? new Date(result.analysis_date).toLocaleString('zh-CN') : 'Invalid Date';

        html += `
            <tr>
                <td><strong>${result.stock_code}</strong></td>
                <td>${result.company_name || ''}</td>
                <td title="${result.file_name || ''}">${(result.file_name || '').substring(0, 30)}${(result.file_name || '').length > 30 ? '...' : ''}</td>
                <td class="keyword-cell">${result.keyword}</td>
                <td class="count-cell ${cellClass}">${count}</td>
                <td>${analysisDate}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

function exportResults() {
    if (!currentTaskId && !currentAnalysisId) {
        alert('没有可导出的结果');
        return;
    }
    
    const taskId = currentTaskId || currentAnalysisId;
    window.open(`/api/export_results/${taskId}`, '_blank');
    addLog('正在导出Excel文件...', 'info');
}

function showSummary() {
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));

    // 获取当前显示的结果数据
    const resultsTable = document.querySelector('.results-table tbody');
    if (!resultsTable) {
        document.getElementById('summaryContent').innerHTML = '<p class="text-muted">没有可统计的数据</p>';
        modal.show();
        return;
    }

    // 解析表格数据生成统计
    const summaryHtml = generateSummaryContent();
    document.getElementById('summaryContent').innerHTML = summaryHtml;
    modal.show();
}

function generateSummaryContent() {
    // 从当前显示的表格中提取数据
    const table = document.querySelector('.results-table');
    if (!table) return '<p class="text-muted">没有数据可统计</p>';

    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr'));

    // 提取关键词列（跳过股票代码和文件名列）
    const keywordHeaders = headers.slice(2);

    // 统计数据
    const stats = {
        totalFiles: rows.length,
        totalStocks: new Set(rows.map(row => row.cells[0].textContent.trim())).size,
        keywordStats: {},
        stockStats: {},
        fileStats: []
    };

    // 初始化关键词统计
    keywordHeaders.forEach(keyword => {
        stats.keywordStats[keyword] = {
            totalCount: 0,
            filesWithKeyword: 0,
            maxCount: 0,
            minCount: Infinity
        };
    });

    // 处理每一行数据
    rows.forEach(row => {
        const stockCode = row.cells[0].textContent.trim();
        const fileName = row.cells[1].textContent.trim();
        const fileStats = { stockCode, fileName, keywords: {} };

        // 初始化股票统计
        if (!stats.stockStats[stockCode]) {
            stats.stockStats[stockCode] = {
                fileCount: 0,
                totalKeywords: 0,
                keywords: {}
            };
        }
        stats.stockStats[stockCode].fileCount++;

        // 处理关键词数据
        keywordHeaders.forEach((keyword, index) => {
            const count = parseInt(row.cells[index + 2].textContent.trim()) || 0;
            fileStats.keywords[keyword] = count;

            // 更新关键词统计
            stats.keywordStats[keyword].totalCount += count;
            if (count > 0) {
                stats.keywordStats[keyword].filesWithKeyword++;
            }
            stats.keywordStats[keyword].maxCount = Math.max(stats.keywordStats[keyword].maxCount, count);
            stats.keywordStats[keyword].minCount = Math.min(stats.keywordStats[keyword].minCount, count);

            // 更新股票统计
            if (!stats.stockStats[stockCode].keywords[keyword]) {
                stats.stockStats[stockCode].keywords[keyword] = 0;
            }
            stats.stockStats[stockCode].keywords[keyword] += count;
            stats.stockStats[stockCode].totalKeywords += count;
        });

        stats.fileStats.push(fileStats);
    });

    // 修正最小值（如果没有出现过关键词）
    Object.keys(stats.keywordStats).forEach(keyword => {
        if (stats.keywordStats[keyword].minCount === Infinity) {
            stats.keywordStats[keyword].minCount = 0;
        }
    });

    return buildSummaryHTML(stats, keywordHeaders);
}

function buildSummaryHTML(stats, keywordHeaders) {
    let html = '<div class="summary-content">';

    // 总体统计
    html += `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card bg-primary text-white">
                    <h3>${stats.totalStocks}</h3>
                    <p>股票数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-success text-white">
                    <h3>${stats.totalFiles}</h3>
                    <p>文件数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-info text-white">
                    <h3>${keywordHeaders.length}</h3>
                    <p>关键词数量</p>
                </div>
            </div>
        </div>
    `;

    // 关键词统计表
    html += '<h5><i class="bi bi-tags"></i> 关键词统计</h5>';
    html += '<div class="table-responsive mb-4">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>关键词</th><th>总出现次数</th><th>出现文件数</th><th>最大次数</th><th>平均次数</th><th>覆盖率</th></tr></thead><tbody>';

    keywordHeaders.forEach(keyword => {
        const stat = stats.keywordStats[keyword];
        const avgCount = stat.filesWithKeyword > 0 ? (stat.totalCount / stat.filesWithKeyword).toFixed(1) : 0;
        const coverage = ((stat.filesWithKeyword / stats.totalFiles) * 100).toFixed(1);
        const rowClass = stat.totalCount === 0 ? 'table-secondary' : '';

        html += `
            <tr class="${rowClass}">
                <td><strong>${keyword}</strong></td>
                <td class="text-center">${stat.totalCount}</td>
                <td class="text-center">${stat.filesWithKeyword}</td>
                <td class="text-center">${stat.maxCount}</td>
                <td class="text-center">${avgCount}</td>
                <td class="text-center">${coverage}%</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 股票统计表
    html += '<h5><i class="bi bi-building"></i> 股票统计</h5>';
    html += '<div class="table-responsive mb-4">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>股票代码</th><th>文件数</th><th>关键词总数</th><th>平均每文件</th></tr></thead><tbody>';

    Object.entries(stats.stockStats).forEach(([stockCode, stockStat]) => {
        const avgPerFile = stockStat.fileCount > 0 ? (stockStat.totalKeywords / stockStat.fileCount).toFixed(1) : 0;
        html += `
            <tr>
                <td><strong>${stockCode}</strong></td>
                <td class="text-center">${stockStat.fileCount}</td>
                <td class="text-center">${stockStat.totalKeywords}</td>
                <td class="text-center">${avgPerFile}</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 零出现关键词
    const zeroKeywords = keywordHeaders.filter(keyword => stats.keywordStats[keyword].totalCount === 0);
    if (zeroKeywords.length > 0) {
        html += '<h5><i class="bi bi-exclamation-triangle text-warning"></i> 未出现的关键词</h5>';
        html += '<div class="alert alert-warning">';
        html += '<p>以下关键词在所有文件中都没有出现：</p>';
        html += '<div class="d-flex flex-wrap gap-2">';
        zeroKeywords.forEach(keyword => {
            html += `<span class="badge bg-warning text-dark">${keyword}</span>`;
        });
        html += '</div></div>';
    }

    html += '</div>';
    return html;
}

// 分页相关变量
let currentPage = 1;
let itemsPerPage = 10;
let allTableRows = [];

function addPaginationToResults(tableHtml) {
    // 解析表格HTML，提取行数据
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = tableHtml;
    const table = tempDiv.querySelector('table');

    if (!table) return tableHtml;

    const tbody = table.querySelector('tbody');
    if (!tbody) return tableHtml;

    allTableRows = Array.from(tbody.querySelectorAll('tr'));
    const totalItems = allTableRows.length;

    if (totalItems <= itemsPerPage) {
        // 如果数据少于一页，不需要分页
        return tableHtml;
    }

    // 生成分页后的HTML
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    currentPage = Math.min(currentPage, totalPages); // 确保当前页不超过总页数

    // 获取当前页的数据
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 重新构建表格
    const thead = table.querySelector('thead');
    let paginatedHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    paginatedHtml += thead.outerHTML;
    paginatedHtml += '<tbody>';
    currentPageRows.forEach(row => {
        paginatedHtml += row.outerHTML;
    });
    paginatedHtml += '</tbody></table></div>';

    // 添加分页控件
    paginatedHtml += generatePaginationControls(currentPage, totalPages, totalItems);

    return paginatedHtml;
}

function generatePaginationControls(page, totalPages, totalItems) {
    const startItem = (page - 1) * itemsPerPage + 1;
    const endItem = Math.min(page * itemsPerPage, totalItems);

    let html = '<div class="d-flex justify-content-between align-items-center mt-3">';

    // 显示信息
    html += `<div class="text-muted">显示 ${startItem}-${endItem} 条，共 ${totalItems} 条记录</div>`;

    // 分页控件
    html += '<nav><ul class="pagination pagination-sm mb-0">';

    // 上一页
    const prevDisabled = page === 1 ? 'disabled' : '';
    html += `<li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page - 1}); return false;">上一页</a>
             </li>`;

    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);

    if (startPage > 1) {
        html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>';
        if (startPage > 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === page ? 'active' : '';
        html += `<li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                 </li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    const nextDisabled = page === totalPages ? 'disabled' : '';
    html += `<li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page + 1}); return false;">下一页</a>
             </li>`;

    html += '</ul></nav></div>';

    return html;
}

function changePage(page) {
    if (page < 1 || !allTableRows.length) return;

    const totalPages = Math.ceil(allTableRows.length / itemsPerPage);
    if (page > totalPages) return;

    currentPage = page;

    // 重新生成当前页的表格
    const resultsContent = document.getElementById('resultsContent');
    const table = resultsContent.querySelector('table');
    const thead = table.querySelector('thead');

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allTableRows.length);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 更新表格内容
    let newHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    newHtml += thead.outerHTML;
    newHtml += '<tbody>';
    currentPageRows.forEach(row => {
        newHtml += row.outerHTML;
    });
    newHtml += '</tbody></table></div>';

    // 更新分页控件
    newHtml += generatePaginationControls(currentPage, totalPages, allTableRows.length);

    resultsContent.innerHTML = newHtml;

    // 滚动到表格顶部
    document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
}

function showKeywordContext(keyword, stockCode, analysisId = null) {
    const useAnalysisId = analysisId || currentAnalysisId || currentTaskId;

    console.log('showKeywordContext 调用参数:', {
        keyword, stockCode, analysisId,
        currentAnalysisId, currentTaskId, useAnalysisId
    });

    if (!useAnalysisId) {
        alert('没有可用的分析ID');
        return;
    }

    showLoading('正在获取上下文...');
    addLog(`正在获取关键词"${keyword}"的上下文...`, 'info');

    fetch(`/api/keyword_context/${useAnalysisId}/${encodeURIComponent(keyword)}`)
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭加载弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        console.log('上下文API响应:', data);
        if (data.success) {
            if (data.contexts && data.contexts.length > 0) {
                displayKeywordContext(data.keyword, data.contexts, stockCode);
                addLog(`成功获取到 ${data.contexts.length} 个上下文片段`, 'success');
            } else {
                addLog(`关键词"${keyword}"没有找到上下文内容`, 'warning');
                alert(`关键词"${keyword}"没有找到上下文内容`);
            }
        } else {
            addLog(`获取上下文失败: ${data.message}`, 'error');
            alert(`获取上下文失败: ${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭加载弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);

        console.error('获取上下文失败:', error);
        addLog(`获取上下文请求失败: ${error.message}`, 'error');
        alert(`获取上下文失败: ${error.message}`);
    });
}

function displayKeywordContext(keyword, contexts, targetStockCode) {
    // 过滤指定股票的上下文
    const filteredContexts = targetStockCode ?
        contexts.filter(ctx => ctx.stock_code === targetStockCode) :
        contexts;

    // 计算总片段数
    const totalSnippets = filteredContexts.reduce((sum, context) => sum + (context.snippets ? context.snippets.length : 0), 0);

    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0"><i class="bi bi-search"></i> 关键词"${keyword}"的上下文</h6>
            <span class="badge bg-info">${totalSnippets} 个片段</span>
        </div>
    `;

    if (filteredContexts.length === 0) {
        html += '<p class="text-muted">没有找到相关上下文</p>';
    } else {
        filteredContexts.forEach((context, contextIndex) => {
            const snippetCount = context.snippets ? context.snippets.length : 0;
            const keywordCount = context.keyword_count || context.snippets?.length || 0;

            html += `
                <div class="card mb-3" id="context-card-${contextIndex}">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="bi bi-building"></i> ${context.stock_code} - ${context.company_name}
                            </h6>
                            <span class="badge bg-secondary">${keywordCount} 次出现</span>
                        </div>
                        <small class="text-muted">${context.file_name}</small>
                    </div>
                    <div class="card-body">
            `;

            if (context.snippets && context.snippets.length > 0) {
                // 如果片段数量超过5个，添加分页控制
                if (snippetCount > 5) {
                    html += `
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">共 ${snippetCount} 个片段</small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="showAllSnippets(${contextIndex})">
                                        <i class="bi bi-list"></i> 显示全部
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary active" onclick="showPaginatedSnippets(${contextIndex})">
                                        <i class="bi bi-collection"></i> 分页显示
                                    </button>
                                </div>
                            </div>
                            <div id="snippets-container-${contextIndex}">
                                <!-- 片段内容将在这里动态加载 -->
                            </div>
                            <div id="pagination-${contextIndex}" class="d-flex justify-content-center mt-3">
                                <!-- 分页控件将在这里动态加载 -->
                            </div>
                        </div>
                    `;

                    // 存储片段数据到全局变量
                    if (!window.contextSnippets) window.contextSnippets = {};
                    window.contextSnippets[contextIndex] = context.snippets;
                } else {
                    // 少于5个片段，直接显示
                    context.snippets.forEach((snippet, index) => {
                        html += `
                            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                                <small class="text-muted">片段 ${index + 1}:</small>
                                <div class="mt-1">${snippet}</div>
                            </div>
                        `;
                    });
                }
            } else {
                html += '<p class="text-muted">没有找到相关上下文</p>';
            }

            html += '</div></div>';
        });
    }

    // 显示在模态框中
    document.getElementById('summaryContent').innerHTML = html;
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
    document.querySelector('#summaryModal .modal-title').innerHTML = `<i class="bi bi-text-paragraph"></i> 上下文查看`;
    modal.show();

    // 初始化分页显示
    setTimeout(() => {
        filteredContexts.forEach((context, contextIndex) => {
            if (context.snippets && context.snippets.length > 5) {
                showSnippetPage(contextIndex, 1);
            }
        });
    }, 100);
}

function cleanDuplicates() {
    if (!confirm('确定要清理重复数据吗？此操作不可撤销。')) {
        return;
    }

    showLoading('正在清理重复数据...');
    addLog('开始清理重复数据...', 'info');

    fetch('/api/clean_duplicates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            addLog(`清理完成: ${data.message}`, 'success');

            // 如果清理了数据，建议刷新页面
            if (data.reports_cleaned > 0 || data.analysis_cleaned > 0) {
                if (confirm('数据已清理完成，是否刷新页面以查看最新结果？')) {
                    location.reload();
                }
            }
        } else {
            addLog(`清理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('清理重复数据失败:', error);
        addLog(`清理失败: ${error.message}`, 'error');
    });
}

// 分页显示片段的函数
function showSnippetPage(contextIndex, page) {
    const snippets = window.contextSnippets[contextIndex];
    if (!snippets) return;

    const pageSize = 5;
    const totalPages = Math.ceil(snippets.length / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, snippets.length);

    const containerId = `snippets-container-${contextIndex}`;
    const paginationId = `pagination-${contextIndex}`;

    // 显示当前页的片段
    let snippetsHtml = '';
    for (let i = startIndex; i < endIndex; i++) {
        snippetsHtml += `
            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                <small class="text-muted">片段 ${i + 1}:</small>
                <div class="mt-1">${snippets[i]}</div>
            </div>
        `;
    }

    document.getElementById(containerId).innerHTML = snippetsHtml;

    // 生成分页控件
    let paginationHtml = '';
    if (totalPages > 1) {
        paginationHtml = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

        // 上一页
        if (page > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${page - 1}); return false;">上一页</a></li>`;
        }

        // 页码（显示当前页前后2页）
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, 1); return false;">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${i}); return false;">${i}</a></li>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${totalPages}); return false;">${totalPages}</a></li>`;
        }

        // 下一页
        if (page < totalPages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${page + 1}); return false;">下一页</a></li>`;
        }

        paginationHtml += '</ul></nav>';

        // 添加页面信息
        paginationHtml += `<small class="text-muted text-center d-block mt-2">第 ${page} 页，共 ${totalPages} 页</small>`;
    }

    document.getElementById(paginationId).innerHTML = paginationHtml;
}

function showAllSnippets(contextIndex) {
    const snippets = window.contextSnippets[contextIndex];
    if (!snippets) return;

    const containerId = `snippets-container-${contextIndex}`;
    const paginationId = `pagination-${contextIndex}`;

    // 显示所有片段
    let snippetsHtml = '';
    snippets.forEach((snippet, index) => {
        snippetsHtml += `
            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                <small class="text-muted">片段 ${index + 1}:</small>
                <div class="mt-1">${snippet}</div>
            </div>
        `;
    });

    document.getElementById(containerId).innerHTML = snippetsHtml;
    document.getElementById(paginationId).innerHTML = `<small class="text-muted text-center d-block">显示全部 ${snippets.length} 个片段</small>`;

    // 更新按钮状态
    const cardElement = document.querySelector(`#context-card-${contextIndex}`);
    if (cardElement) {
        const buttons = cardElement.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[0].classList.add('active'); // "显示全部"按钮
    }
}

function showPaginatedSnippets(contextIndex) {
    showSnippetPage(contextIndex, 1);

    // 更新按钮状态
    const cardElement = document.querySelector(`#context-card-${contextIndex}`);
    if (cardElement) {
        const buttons = cardElement.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[1].classList.add('active'); // "分页显示"按钮
    }
}

function displayRelatedPartyAnalysis(relatedPartyData) {
    // 创建关联方分析结果卡片
    const resultsCard = document.getElementById('resultsCard');

    // 在结果卡片后面添加关联方分析卡片
    let relatedPartyCard = document.getElementById('relatedPartyCard');
    if (!relatedPartyCard) {
        relatedPartyCard = document.createElement('div');
        relatedPartyCard.id = 'relatedPartyCard';
        relatedPartyCard.className = 'card mt-3';
        relatedPartyCard.innerHTML = `
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people-fill"></i> 关联方协同创新分析
                </h5>
            </div>
            <div class="card-body" id="relatedPartyContent">
                <!-- 关联方分析内容 -->
            </div>
        `;
        resultsCard.parentNode.insertBefore(relatedPartyCard, resultsCard.nextSibling);
    }

    const content = generateRelatedPartyHTML(relatedPartyData);
    document.getElementById('relatedPartyContent').innerHTML = content;
    relatedPartyCard.style.display = 'block';
}

function generateRelatedPartyHTML(relatedPartyData) {
    let html = '';

    // 统计总体信息
    let totalParties = 0;
    let partiesWithInnovation = 0;
    let totalInnovationRelations = 0;

    Object.values(relatedPartyData).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.values(fileData).forEach(partyData => {
                totalParties++;
                if (partyData.has_innovation) {
                    partiesWithInnovation++;
                    totalInnovationRelations += partyData.innovation_contexts.length;
                }
            });
        });
    });

    // 总体统计
    html += `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-info text-white p-3 rounded text-center">
                    <h4>${totalParties}</h4>
                    <p class="mb-0">关联方总数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success text-white p-3 rounded text-center">
                    <h4>${partiesWithInnovation}</h4>
                    <p class="mb-0">有协同创新</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning text-white p-3 rounded text-center">
                    <h4>${totalParties - partiesWithInnovation}</h4>
                    <p class="mb-0">无协同创新</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-primary text-white p-3 rounded text-center">
                    <h4>${totalInnovationRelations}</h4>
                    <p class="mb-0">创新关系总数</p>
                </div>
            </div>
        </div>
    `;

    // 详细分析结果
    html += '<h6><i class="bi bi-list-ul"></i> 详细分析结果</h6>';

    Object.entries(relatedPartyData).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-building"></i> ${stockCode}
                            <small class="text-muted ms-2">${fileName}</small>
                        </h6>
                    </div>
                    <div class="card-body">
            `;

            Object.entries(fileData).forEach(([partyName, partyData]) => {
                const statusBadge = partyData.has_innovation ?
                    '<span class="badge bg-success">有协同创新</span>' :
                    '<span class="badge bg-secondary">无协同创新</span>';

                html += `
                    <div class="border rounded p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-person-badge"></i> ${partyData.party_name}
                            </h6>
                            ${statusBadge}
                        </div>
                `;

                if (partyData.has_innovation) {
                    html += `
                        <div class="mb-2">
                            <small class="text-muted">涉及创新关键词：</small>
                            <div class="mt-1">
                    `;
                    partyData.innovation_keywords_found.forEach(keyword => {
                        html += `<span class="badge bg-primary me-1">${keyword}</span>`;
                    });
                    html += '</div></div>';

                    // 显示创新上下文
                    html += '<div class="mt-2">';
                    html += `<small class="text-muted">协同创新上下文 (${partyData.innovation_contexts.length} 处)：</small>`;

                    partyData.innovation_contexts.forEach((context, index) => {
                        html += `
                            <div class="alert alert-light border-start border-success border-3 mt-2">
                                <small class="text-muted">上下文 ${index + 1}:</small>
                                <div class="mt-1">${highlightInnovationKeywords(context.context, context.innovation_keywords)}</div>
                                <small class="text-muted">涉及关键词: ${context.innovation_keywords.join(', ')}</small>
                            </div>
                        `;
                    });
                    html += '</div>';
                } else {
                    html += '<small class="text-muted">在年报中找到该关联方，但未发现协同创新关系</small>';
                }

                html += '</div>';
            });

            html += '</div></div>';
        });
    });

    return html;
}

function highlightInnovationKeywords(text, keywords) {
    let highlightedText = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'g');
        highlightedText = highlightedText.replace(regex, `<mark class="bg-success text-white">${keyword}</mark>`);
    });
    return highlightedText;
}


