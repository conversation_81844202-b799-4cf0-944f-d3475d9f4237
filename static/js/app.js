// 全局变量
let currentTaskId = null;
let progressInterval = null;
let currentAnalysisId = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化界面状态
    updateDataSourceVisibility();
    
    // 添加初始日志
    addLog('系统已就绪，请选择操作...', 'info');
}

function bindEventListeners() {
    // 数据源选择变化
    document.querySelectorAll('input[name="dataSource"]').forEach(radio => {
        radio.addEventListener('change', updateDataSourceVisibility);
    });
    
    // 按钮事件
    document.getElementById('startAnalysisBtn').addEventListener('click', startAnalysis);
    document.getElementById('stopAnalysisBtn').addEventListener('click', stopAnalysis);
    document.getElementById('keywordOnlyBtn').addEventListener('click', keywordOnlyAnalysis);
    document.getElementById('importTxtBtn').addEventListener('click', importTxtFiles);
    document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    document.getElementById('exportResultsBtn').addEventListener('click', exportResults);
    document.getElementById('showSummaryBtn').addEventListener('click', showSummary);
    document.getElementById('cleanDuplicatesBtn').addEventListener('click', cleanDuplicates);
}

function updateDataSourceVisibility() {
    const isOnline = document.getElementById('onlineSource').checked;
    const searchKeywordGroup = document.getElementById('searchKeywordGroup');
    const dateRangeGroup = document.getElementById('dateRangeGroup');
    
    if (isOnline) {
        searchKeywordGroup.style.display = 'block';
        dateRangeGroup.style.display = 'block';
    } else {
        searchKeywordGroup.style.display = 'none';
        dateRangeGroup.style.display = 'none';
    }
}

function addLog(message, type = 'info') {
    const logOutput = document.getElementById('logOutput');
    const timestamp = new Date().toLocaleString('zh-CN');
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `
        <span class="timestamp">[${timestamp}]</span>
        <span class="message">${message}</span>
    `;
    
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

function clearLog() {
    const logOutput = document.getElementById('logOutput');
    logOutput.innerHTML = '';
    addLog('日志已清空', 'info');
}

function showLoading(text = '处理中...') {
    document.getElementById('loadingText').textContent = text;
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    try {
        const loadingModal = document.getElementById('loadingModal');

        // 方法1: 使用Bootstrap Modal API
        const modal = bootstrap.Modal.getInstance(loadingModal);
        if (modal) {
            modal.hide();
        }

        // 方法2: 强制关闭（确保弹窗一定关闭）
        setTimeout(() => {
            if (loadingModal) {
                loadingModal.style.display = 'none';
                loadingModal.classList.remove('show');
                loadingModal.setAttribute('aria-hidden', 'true');
                loadingModal.removeAttribute('aria-modal');
                loadingModal.removeAttribute('role');
            }

            // 移除body的modal相关类
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // 移除所有backdrop
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
        }, 50);

    } catch (error) {
        console.error('关闭加载弹窗失败:', error);
    }
}

function updateProgress(progress, message, status = 'running') {
    const progressCard = document.getElementById('progressCard');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const taskStatus = document.getElementById('taskStatus');
    
    progressCard.style.display = 'block';
    progressBar.style.width = `${progress}%`;
    progressBar.textContent = `${progress}%`;
    progressText.textContent = message;
    
    // 更新状态徽章
    let badgeClass = 'bg-secondary';
    let statusText = '等待中';
    
    switch (status) {
        case 'running':
            badgeClass = 'bg-primary';
            statusText = '运行中';
            break;
        case 'completed':
            badgeClass = 'bg-success';
            statusText = '已完成';
            break;
        case 'error':
            badgeClass = 'bg-danger';
            statusText = '错误';
            break;
        case 'stopped':
            badgeClass = 'bg-warning';
            statusText = '已停止';
            break;
    }
    
    taskStatus.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;
}

function startAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();
    const searchKeyword = document.getElementById('searchKeyword').value.trim();
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const useOnline = document.getElementById('onlineSource').checked;
    
    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }
    
    if (!keywords) {
        alert('请输入关键词');
        return;
    }
    
    // 禁用开始按钮，启用停止按钮
    document.getElementById('startAnalysisBtn').disabled = true;
    document.getElementById('stopAnalysisBtn').disabled = false;
    
    addLog('开始分析任务...', 'info');
    
    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        search_keyword: searchKeyword,
        start_date: startDate,
        end_date: endDate,
        use_online: useOnline
    };
    
    fetch('/api/start_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentTaskId = data.task_id;
            addLog(`任务已启动，任务ID: ${currentTaskId}`, 'success');
            startProgressMonitoring();
        } else {
            addLog(`启动失败: ${data.message}`, 'error');
            resetButtons();
        }
    })
    .catch(error => {
        addLog(`请求失败: ${error.message}`, 'error');
        resetButtons();
    });
}

function stopAnalysis() {
    if (!currentTaskId) {
        return;
    }
    
    fetch(`/api/stop_task/${currentTaskId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('任务已停止', 'warning');
        } else {
            addLog(`停止失败: ${data.message}`, 'error');
        }
        stopProgressMonitoring();
        resetButtons();
    })
    .catch(error => {
        addLog(`停止请求失败: ${error.message}`, 'error');
    });
}

function keywordOnlyAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入关键词');
        return;
    }

    showLoading('正在分析关键词...');
    addLog('开始关键词分析...', 'info');

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords
    };

    fetch('/api/keyword_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 确保关闭加载弹窗
        hideLoading();

        if (data.success) {
            currentAnalysisId = data.analysis_id;
            addLog(`关键词分析完成: ${data.message}`, 'success');
            displayResults(data.data);

            // 启用导出和摘要按钮
            document.getElementById('exportResultsBtn').disabled = false;
            document.getElementById('showSummaryBtn').disabled = false;
        } else {
            addLog(`分析失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('关键词分析请求失败:', error);
        // 确保在错误情况下也关闭加载弹窗
        hideLoading();
        addLog(`分析请求失败: ${error.message}`, 'error');

        // 强制关闭弹窗
        setTimeout(() => {
            hideLoading();
        }, 100);
    });
}

function importTxtFiles() {
    showLoading('正在导入TXT文件...');
    addLog('开始导入TXT文件到数据库...', 'info');
    
    fetch('/api/import_txt', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({txt_dir: 'txt'})
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            addLog(`导入成功: ${data.message}`, 'success');
        } else {
            addLog(`导入失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        addLog(`导入请求失败: ${error.message}`, 'error');
    });
}

function startProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    progressInterval = setInterval(() => {
        if (!currentTaskId) {
            stopProgressMonitoring();
            return;
        }
        
        fetch(`/api/task_status/${currentTaskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskData = data.data;
                updateProgress(taskData.progress, taskData.message, taskData.status);
                
                if (taskData.status === 'completed') {
                    addLog('任务完成！', 'success');
                    stopProgressMonitoring();
                    resetButtons();
                    loadTaskResults();
                } else if (taskData.status === 'error') {
                    addLog(`任务失败: ${taskData.message}`, 'error');
                    stopProgressMonitoring();
                    resetButtons();
                } else if (taskData.status === 'stopped') {
                    addLog('任务已停止', 'warning');
                    stopProgressMonitoring();
                    resetButtons();
                }
            }
        })
        .catch(error => {
            console.error('获取任务状态失败:', error);
        });
    }, 2000); // 每2秒检查一次
}

function stopProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

function resetButtons() {
    document.getElementById('startAnalysisBtn').disabled = false;
    document.getElementById('stopAnalysisBtn').disabled = true;
}

function loadTaskResults() {
    if (!currentTaskId) {
        return;
    }
    
    fetch(`/api/analysis_results/${currentTaskId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResults(data.data);
        } else {
            addLog(`获取结果失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`获取结果请求失败: ${error.message}`, 'error');
    });
}

function displayResults(results) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');

    // 调试信息
    console.log('displayResults 收到的数据:', results);
    console.log('数据类型:', typeof results);
    console.log('是否为数组:', Array.isArray(results));

    if (!results) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    // 检查数据格式
    if (Array.isArray(results) && results.length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    if (typeof results === 'object' && !Array.isArray(results) && Object.keys(results).length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    let html = '';

    // 如果是数组格式（从数据库获取的结果）
    if (Array.isArray(results)) {
        console.log('使用数组格式处理，数据长度:', results.length);
        if (results.length > 0) {
            console.log('第一条数据示例:', results[0]);
        }
        html = generateTableFromArray(results);
    } else {
        // 如果是对象格式（直接分析的结果）
        console.log('使用对象格式处理，键数量:', Object.keys(results).length);
        html = generateTableFromObject(results);
    }

    // 添加分页功能
    const paginatedHtml = addPaginationToResults(html);
    resultsContent.innerHTML = paginatedHtml;
    resultsCard.style.display = 'block';

    // 启用导出和摘要按钮
    const exportBtn = document.getElementById('exportResultsBtn');
    const summaryBtn = document.getElementById('showSummaryBtn');

    if (exportBtn) exportBtn.disabled = false;
    if (summaryBtn) summaryBtn.disabled = false;

    // 添加成功日志
    addLog('结果显示完成，可以查看分析结果', 'success');
}

function generateTableFromObject(results) {
    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th style="min-width: 100px;">股票代码</th><th style="min-width: 200px;">文件名</th>';

    // 获取所有关键词
    const allKeywords = new Set();
    Object.values(results).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.keys(fileData).forEach(keyword => allKeywords.add(keyword));
        });
    });

    // 按关键词长度排序，确保显示一致性
    const sortedKeywords = Array.from(allKeywords).sort();

    sortedKeywords.forEach(keyword => {
        html += `<th class="keyword-cell text-center" style="min-width: 80px; font-weight: bold; background-color: #f8f9fa;">${keyword}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 生成数据行
    Object.entries(results).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            html += `<tr><td><strong>${stockCode}</strong></td><td title="${fileName}">${fileName}</td>`;
            sortedKeywords.forEach(keyword => {
                const count = fileData[keyword] || 0;
                const cellClass = count === 0 ? 'count-zero' : (count > 10 ? 'count-high' : '');
                let cellContent = count;

                // 如果有出现次数，添加查看上下文按钮
                if (count > 0 && currentAnalysisId) {
                    cellContent = `
                        <div class="d-flex align-items-center justify-content-center">
                            <span class="me-2">${count}</span>
                            <button class="btn btn-sm btn-outline-info" onclick="showKeywordContext('${keyword}', '${stockCode}')" title="查看上下文">
                                <i class="bi bi-eye" style="font-size: 0.8em;"></i>
                            </button>
                        </div>
                    `;
                }

                html += `<td class="count-cell text-center ${cellClass}" title="${keyword}: ${count}次">${cellContent}</td>`;
            });
            html += '</tr>';
        });
    });
    
    html += '</tbody></table></div>';
    return html;
}

function generateTableFromArray(results) {
    if (results.length === 0) {
        return '<p class="text-muted">没有分析结果</p>';
    }

    // 前端去重：基于 stock_code + file_name + keyword 的组合
    const uniqueResults = [];
    const seen = new Set();

    results.forEach(result => {
        const key = `${result.stock_code}_${result.file_name}_${result.keyword}`;
        if (!seen.has(key)) {
            seen.add(key);
            uniqueResults.push(result);
        }
    });

    console.log(`原始数据: ${results.length} 条，去重后: ${uniqueResults.length} 条`);

    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th>股票代码</th><th>公司名称</th><th>文件名</th><th>关键词</th><th>出现次数</th><th>分析日期</th></tr></thead><tbody>';

    uniqueResults.forEach(result => {
        const count = result.count || 0;
        const cellClass = count === 0 ? 'count-zero' : (count > 10 ? 'count-high' : '');
        const analysisDate = result.analysis_date ? new Date(result.analysis_date).toLocaleString('zh-CN') : 'Invalid Date';

        html += `
            <tr>
                <td><strong>${result.stock_code}</strong></td>
                <td>${result.company_name || ''}</td>
                <td title="${result.file_name || ''}">${(result.file_name || '').substring(0, 30)}${(result.file_name || '').length > 30 ? '...' : ''}</td>
                <td class="keyword-cell">${result.keyword}</td>
                <td class="count-cell ${cellClass}">${count}</td>
                <td>${analysisDate}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

function exportResults() {
    if (!currentTaskId && !currentAnalysisId) {
        alert('没有可导出的结果');
        return;
    }
    
    const taskId = currentTaskId || currentAnalysisId;
    window.open(`/api/export_results/${taskId}`, '_blank');
    addLog('正在导出Excel文件...', 'info');
}

function showSummary() {
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));

    // 获取当前显示的结果数据
    const resultsTable = document.querySelector('.results-table tbody');
    if (!resultsTable) {
        document.getElementById('summaryContent').innerHTML = '<p class="text-muted">没有可统计的数据</p>';
        modal.show();
        return;
    }

    // 解析表格数据生成统计
    const summaryHtml = generateSummaryContent();
    document.getElementById('summaryContent').innerHTML = summaryHtml;
    modal.show();
}

function generateSummaryContent() {
    // 从当前显示的表格中提取数据
    const table = document.querySelector('.results-table');
    if (!table) return '<p class="text-muted">没有数据可统计</p>';

    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr'));

    // 提取关键词列（跳过股票代码和文件名列）
    const keywordHeaders = headers.slice(2);

    // 统计数据
    const stats = {
        totalFiles: rows.length,
        totalStocks: new Set(rows.map(row => row.cells[0].textContent.trim())).size,
        keywordStats: {},
        stockStats: {},
        fileStats: []
    };

    // 初始化关键词统计
    keywordHeaders.forEach(keyword => {
        stats.keywordStats[keyword] = {
            totalCount: 0,
            filesWithKeyword: 0,
            maxCount: 0,
            minCount: Infinity
        };
    });

    // 处理每一行数据
    rows.forEach(row => {
        const stockCode = row.cells[0].textContent.trim();
        const fileName = row.cells[1].textContent.trim();
        const fileStats = { stockCode, fileName, keywords: {} };

        // 初始化股票统计
        if (!stats.stockStats[stockCode]) {
            stats.stockStats[stockCode] = {
                fileCount: 0,
                totalKeywords: 0,
                keywords: {}
            };
        }
        stats.stockStats[stockCode].fileCount++;

        // 处理关键词数据
        keywordHeaders.forEach((keyword, index) => {
            const count = parseInt(row.cells[index + 2].textContent.trim()) || 0;
            fileStats.keywords[keyword] = count;

            // 更新关键词统计
            stats.keywordStats[keyword].totalCount += count;
            if (count > 0) {
                stats.keywordStats[keyword].filesWithKeyword++;
            }
            stats.keywordStats[keyword].maxCount = Math.max(stats.keywordStats[keyword].maxCount, count);
            stats.keywordStats[keyword].minCount = Math.min(stats.keywordStats[keyword].minCount, count);

            // 更新股票统计
            if (!stats.stockStats[stockCode].keywords[keyword]) {
                stats.stockStats[stockCode].keywords[keyword] = 0;
            }
            stats.stockStats[stockCode].keywords[keyword] += count;
            stats.stockStats[stockCode].totalKeywords += count;
        });

        stats.fileStats.push(fileStats);
    });

    // 修正最小值（如果没有出现过关键词）
    Object.keys(stats.keywordStats).forEach(keyword => {
        if (stats.keywordStats[keyword].minCount === Infinity) {
            stats.keywordStats[keyword].minCount = 0;
        }
    });

    return buildSummaryHTML(stats, keywordHeaders);
}

function buildSummaryHTML(stats, keywordHeaders) {
    let html = '<div class="summary-content">';

    // 总体统计
    html += `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card bg-primary text-white">
                    <h3>${stats.totalStocks}</h3>
                    <p>股票数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-success text-white">
                    <h3>${stats.totalFiles}</h3>
                    <p>文件数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-info text-white">
                    <h3>${keywordHeaders.length}</h3>
                    <p>关键词数量</p>
                </div>
            </div>
        </div>
    `;

    // 关键词统计表
    html += '<h5><i class="bi bi-tags"></i> 关键词统计</h5>';
    html += '<div class="table-responsive mb-4">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>关键词</th><th>总出现次数</th><th>出现文件数</th><th>最大次数</th><th>平均次数</th><th>覆盖率</th></tr></thead><tbody>';

    keywordHeaders.forEach(keyword => {
        const stat = stats.keywordStats[keyword];
        const avgCount = stat.filesWithKeyword > 0 ? (stat.totalCount / stat.filesWithKeyword).toFixed(1) : 0;
        const coverage = ((stat.filesWithKeyword / stats.totalFiles) * 100).toFixed(1);
        const rowClass = stat.totalCount === 0 ? 'table-secondary' : '';

        html += `
            <tr class="${rowClass}">
                <td><strong>${keyword}</strong></td>
                <td class="text-center">${stat.totalCount}</td>
                <td class="text-center">${stat.filesWithKeyword}</td>
                <td class="text-center">${stat.maxCount}</td>
                <td class="text-center">${avgCount}</td>
                <td class="text-center">${coverage}%</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 股票统计表
    html += '<h5><i class="bi bi-building"></i> 股票统计</h5>';
    html += '<div class="table-responsive mb-4">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>股票代码</th><th>文件数</th><th>关键词总数</th><th>平均每文件</th></tr></thead><tbody>';

    Object.entries(stats.stockStats).forEach(([stockCode, stockStat]) => {
        const avgPerFile = stockStat.fileCount > 0 ? (stockStat.totalKeywords / stockStat.fileCount).toFixed(1) : 0;
        html += `
            <tr>
                <td><strong>${stockCode}</strong></td>
                <td class="text-center">${stockStat.fileCount}</td>
                <td class="text-center">${stockStat.totalKeywords}</td>
                <td class="text-center">${avgPerFile}</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 零出现关键词
    const zeroKeywords = keywordHeaders.filter(keyword => stats.keywordStats[keyword].totalCount === 0);
    if (zeroKeywords.length > 0) {
        html += '<h5><i class="bi bi-exclamation-triangle text-warning"></i> 未出现的关键词</h5>';
        html += '<div class="alert alert-warning">';
        html += '<p>以下关键词在所有文件中都没有出现：</p>';
        html += '<div class="d-flex flex-wrap gap-2">';
        zeroKeywords.forEach(keyword => {
            html += `<span class="badge bg-warning text-dark">${keyword}</span>`;
        });
        html += '</div></div>';
    }

    html += '</div>';
    return html;
}

// 分页相关变量
let currentPage = 1;
let itemsPerPage = 10;
let allTableRows = [];

function addPaginationToResults(tableHtml) {
    // 解析表格HTML，提取行数据
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = tableHtml;
    const table = tempDiv.querySelector('table');

    if (!table) return tableHtml;

    const tbody = table.querySelector('tbody');
    if (!tbody) return tableHtml;

    allTableRows = Array.from(tbody.querySelectorAll('tr'));
    const totalItems = allTableRows.length;

    if (totalItems <= itemsPerPage) {
        // 如果数据少于一页，不需要分页
        return tableHtml;
    }

    // 生成分页后的HTML
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    currentPage = Math.min(currentPage, totalPages); // 确保当前页不超过总页数

    // 获取当前页的数据
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 重新构建表格
    const thead = table.querySelector('thead');
    let paginatedHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    paginatedHtml += thead.outerHTML;
    paginatedHtml += '<tbody>';
    currentPageRows.forEach(row => {
        paginatedHtml += row.outerHTML;
    });
    paginatedHtml += '</tbody></table></div>';

    // 添加分页控件
    paginatedHtml += generatePaginationControls(currentPage, totalPages, totalItems);

    return paginatedHtml;
}

function generatePaginationControls(page, totalPages, totalItems) {
    const startItem = (page - 1) * itemsPerPage + 1;
    const endItem = Math.min(page * itemsPerPage, totalItems);

    let html = '<div class="d-flex justify-content-between align-items-center mt-3">';

    // 显示信息
    html += `<div class="text-muted">显示 ${startItem}-${endItem} 条，共 ${totalItems} 条记录</div>`;

    // 分页控件
    html += '<nav><ul class="pagination pagination-sm mb-0">';

    // 上一页
    const prevDisabled = page === 1 ? 'disabled' : '';
    html += `<li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page - 1}); return false;">上一页</a>
             </li>`;

    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);

    if (startPage > 1) {
        html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>';
        if (startPage > 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === page ? 'active' : '';
        html += `<li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                 </li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    const nextDisabled = page === totalPages ? 'disabled' : '';
    html += `<li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page + 1}); return false;">下一页</a>
             </li>`;

    html += '</ul></nav></div>';

    return html;
}

function changePage(page) {
    if (page < 1 || !allTableRows.length) return;

    const totalPages = Math.ceil(allTableRows.length / itemsPerPage);
    if (page > totalPages) return;

    currentPage = page;

    // 重新生成当前页的表格
    const resultsContent = document.getElementById('resultsContent');
    const table = resultsContent.querySelector('table');
    const thead = table.querySelector('thead');

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allTableRows.length);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 更新表格内容
    let newHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    newHtml += thead.outerHTML;
    newHtml += '<tbody>';
    currentPageRows.forEach(row => {
        newHtml += row.outerHTML;
    });
    newHtml += '</tbody></table></div>';

    // 更新分页控件
    newHtml += generatePaginationControls(currentPage, totalPages, allTableRows.length);

    resultsContent.innerHTML = newHtml;

    // 滚动到表格顶部
    document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
}

function showKeywordContext(keyword, stockCode) {
    if (!currentAnalysisId) {
        alert('没有可用的分析ID');
        return;
    }

    showLoading('正在获取上下文...');

    fetch(`/api/keyword_context/${currentAnalysisId}/${encodeURIComponent(keyword)}`)
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            displayKeywordContext(data.keyword, data.contexts, stockCode);
        } else {
            alert(`获取上下文失败: ${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();
        console.error('获取上下文失败:', error);
        alert(`获取上下文失败: ${error.message}`);
    });
}

function displayKeywordContext(keyword, contexts, targetStockCode) {
    // 过滤指定股票的上下文
    const filteredContexts = targetStockCode ?
        contexts.filter(ctx => ctx.stock_code === targetStockCode) :
        contexts;

    let html = `<h6><i class="bi bi-search"></i> 关键词"${keyword}"的上下文</h6>`;

    if (filteredContexts.length === 0) {
        html += '<p class="text-muted">没有找到相关上下文</p>';
    } else {
        filteredContexts.forEach(context => {
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-building"></i> ${context.stock_code} - ${context.company_name}
                            <small class="text-muted ms-2">${context.file_name}</small>
                        </h6>
                    </div>
                    <div class="card-body">
            `;

            context.snippets.forEach((snippet, index) => {
                html += `
                    <div class="mb-2 p-2 bg-light rounded">
                        <small class="text-muted">片段 ${index + 1}:</small>
                        <div class="mt-1">${snippet}</div>
                    </div>
                `;
            });

            html += '</div></div>';
        });
    }

    // 显示在模态框中
    document.getElementById('summaryContent').innerHTML = html;
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
    document.querySelector('#summaryModal .modal-title').innerHTML = `<i class="bi bi-text-paragraph"></i> 上下文查看`;
    modal.show();
}

function cleanDuplicates() {
    if (!confirm('确定要清理重复数据吗？此操作不可撤销。')) {
        return;
    }

    showLoading('正在清理重复数据...');
    addLog('开始清理重复数据...', 'info');

    fetch('/api/clean_duplicates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            addLog(`清理完成: ${data.message}`, 'success');

            // 如果清理了数据，建议刷新页面
            if (data.reports_cleaned > 0 || data.analysis_cleaned > 0) {
                if (confirm('数据已清理完成，是否刷新页面以查看最新结果？')) {
                    location.reload();
                }
            }
        } else {
            addLog(`清理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('清理重复数据失败:', error);
        addLog(`清理失败: ${error.message}`, 'error');
    });
}
