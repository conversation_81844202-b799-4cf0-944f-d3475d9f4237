/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-section h1 {
    margin: 0;
    font-weight: 300;
}

.header-section small {
    opacity: 0.8;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 500;
}

.card-header h5, .card-header h6 {
    margin: 0;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ee82f0 0%, #f3455a 100%);
    transform: translateY(-1px);
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #3d9bfe 0%, #00e0fe 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #495057;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #96e6e2 0%, #fcc4d1 100%);
    transform: translateY(-1px);
    color: #495057;
}

.btn-success {
    background: linear-gradient(135deg, #81FBB8 0%, #28C76F 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #6ff9a6 0%, #24b865 100%);
    transform: translateY(-1px);
}

/* 日志输出样式 */
.log-output {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    height: 300px;
    overflow-y: auto;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #333;
}

.log-entry {
    margin-bottom: 5px;
    line-height: 1.4;
}

.log-entry .timestamp {
    color: #569cd6;
    font-weight: 500;
}

.log-entry .message {
    color: #d4d4d4;
}

.log-entry.success .message {
    color: #4ec9b0;
}

.log-entry.error .message {
    color: #f44747;
}

.log-entry.warning .message {
    color: #ffcc02;
}

.log-entry.info .message {
    color: #9cdcfe;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 500;
    padding: 12px;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 10px 12px;
    border-color: #dee2e6;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    color: #495057;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #81FBB8 0%, #28C76F 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
    color: #495057;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-section {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }
    
    .header-section h1 {
        font-size: 1.5rem;
    }
    
    .log-output {
        height: 200px;
        font-size: 12px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
.log-output::-webkit-scrollbar {
    width: 8px;
}

.log-output::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 表单控件增强 */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 结果表格特殊样式 */
.results-table {
    font-size: 0.9rem;
}

.results-table .keyword-cell {
    font-weight: 600;
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    text-align: center;
    white-space: nowrap;
    min-width: 80px;
}

.results-table .count-cell {
    text-align: center;
    font-weight: 600;
    vertical-align: middle;
}

.results-table .count-zero {
    color: #6c757d;
    opacity: 0.7;
}

.results-table .count-high {
    color: #28a745;
    font-weight: 700;
}

.results-table .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}

.results-table .d-flex {
    gap: 0.25rem;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 300;
    margin: 0;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}
